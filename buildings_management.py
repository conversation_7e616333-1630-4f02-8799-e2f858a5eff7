"""
وحدة إدارة المباني والمرافق
Buildings and Facilities Management Module
"""

import tkinter as tk
from tkinter import ttk, messagebox
import ttkbootstrap as ttk_bs
from datetime import datetime

class BuildingsManagementWindow:
    """نافذة إدارة المباني والمرافق"""
    
    def __init__(self, parent, db_manager, auth_manager):
        self.parent = parent
        self.db_manager = db_manager
        self.auth_manager = auth_manager
        self.window = None
        self.buildings_tree = None
        self.setup_fonts()

    def setup_fonts(self):
        """إعداد الخطوط"""
        self.title_font = ("Segoe UI", 16, "bold")
        self.header_font = ("Segoe UI", 14, "bold")
        self.normal_font = ("Segoe UI", 12)
        self.button_font = ("Segoe UI", 11, "bold")
        self.tree_font = ("Segoe UI", 11)

    def show(self):
        """عرض نافذة إدارة المباني"""
        self.window = tk.Toplevel(self.parent)
        self.window.title("🏢 إدارة المباني والمرافق")
        self.window.geometry("1000x600")
        self.window.resizable(True, True)

        # توسيط النافذة
        self.center_window()

        self.create_interface()
        self.load_buildings()
    
    def center_window(self):
        """توسيط النافذة في الشاشة"""
        self.window.update_idletasks()
        width = self.window.winfo_width()
        height = self.window.winfo_height()
        screen_width = self.window.winfo_screenwidth()
        screen_height = self.window.winfo_screenheight()
        x = (screen_width - width) // 2
        y = (screen_height - height) // 2
        self.window.geometry(f"{width}x{height}+{x}+{y}")
    
    def create_interface(self):
        """إنشاء واجهة إدارة المباني"""
        # عنوان الصفحة
        header_frame = ttk_bs.Frame(self.window)
        header_frame.pack(fill=tk.X, padx=10, pady=(10, 5))

        title_label = ttk_bs.Label(
            header_frame,
            text="🏢 إدارة المباني والمرافق 🏢",
            bootstyle="primary",
            foreground="#1e3a8a"
        )
        title_label.pack(side=tk.LEFT)

        # شريط الأدوات
        toolbar_frame = ttk_bs.LabelFrame(
            self.window,
            text="🛠️ أدوات المباني",
            padding=15,
            bootstyle="info"
        )
        toolbar_frame.pack(fill=tk.X, padx=15, pady=10)

        # الأزرار
        ttk_bs.Button(
            toolbar_frame,
            text="➕ مبنى جديد",
            command=self.add_building,
            bootstyle="success",
            width=18
        ).pack(side=tk.LEFT, padx=8, ipady=8)

        ttk_bs.Button(
            toolbar_frame,
            text="✏️ تعديل",
            command=self.edit_building,
            bootstyle="warning",
            width=15).pack(side=tk.LEFT, padx=8, ipady=8)

        ttk_bs.Button(
            toolbar_frame,
            text="🗑️ حذف",
            command=self.delete_building,
            bootstyle="danger",
            width=15).pack(side=tk.LEFT, padx=8, ipady=8)

        ttk_bs.Button(
            toolbar_frame,
            text="📋 تفاصيل",
            command=self.view_building_details,
            bootstyle="info",
            width=15).pack(side=tk.LEFT, padx=8, ipady=8)

        ttk_bs.Button(
            toolbar_frame,
            text="🔄 تحديث",
            command=self.load_buildings,
            bootstyle="secondary",
            width=15).pack(side=tk.LEFT, padx=8, ipady=8)
        
        # شريط البحث
        search_frame = ttk_bs.LabelFrame(
            self.window,
            text="🔍 البحث والفلترة",
            padding=15,
            bootstyle="secondary"
        )
        search_frame.pack(fill=tk.X, padx=15, pady=10)

        ttk_bs.Label(
            search_frame,
            text="🔍 البحث:"
        ).pack(side=tk.LEFT, padx=(0, 5))

        self.search_entry = ttk_bs.Entry(
            search_frame,
            width=40,
            bootstyle="info"
        )
        self.search_entry.pack(side=tk.LEFT, padx=5, ipady=3)
        self.search_entry.bind('<KeyRelease>', self.filter_buildings)

        ttk_bs.Button(
            search_frame,
            text="🔍 بحث",
            command=self.filter_buildings,
            bootstyle="info",
            width=10
        ).pack(side=tk.LEFT, padx=5, ipady=3)
        
        # جدول المباني
        tree_frame = ttk_bs.LabelFrame(
            self.window,
            text="🏢 قائمة المباني والمرافق",
            padding=15,
            bootstyle="primary"
        )
        tree_frame.pack(fill=tk.BOTH, expand=True, padx=15, pady=10)

        # إنشاء Treeview
        columns = ("id", "name", "type", "location", "floors", "area", "status", "construction_year")
        self.buildings_tree = ttk.Treeview(
            tree_frame,
            columns=columns,
            show="headings",
            height=15
        )

        # تطبيق خط على الجدول
        style = ttk.Style()
        style.configure("Treeview", rowheight=30)
        style.configure("Treeview.Heading", foreground="#1e3a8a")
        
        # تعريف العناوين
        headers = {
            "id": "🆔 المعرف",
            "name": "🏢 اسم المبنى",
            "type": "🏗️ النوع",
            "location": "📍 الموقع",
            "floors": "🏢 الطوابق",
            "area": "📐 المساحة",
            "status": "📊 الحالة",
            "construction_year": "📅 سنة البناء"
        }
        
        for col, header in headers.items():
            self.buildings_tree.heading(col, text=header)
            if col == "id":
                self.buildings_tree.column(col, width=50)
            elif col in ["name", "location"]:
                self.buildings_tree.column(col, width=150)
            else:
                self.buildings_tree.column(col, width=120)
        
        # شريط التمرير
        scrollbar_y = tk.Scrollbar(tree_frame, orient=tk.VERTICAL, command=self.buildings_tree.yview)
        scrollbar_x = tk.Scrollbar(tree_frame, orient=tk.HORIZONTAL, command=self.buildings_tree.xview)
        self.buildings_tree.configure(yscrollcommand=scrollbar_y.set, xscrollcommand=scrollbar_x.set)
        
        # تعبئة الجدول وشريط التمرير
        self.buildings_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar_y.pack(side=tk.RIGHT, fill=tk.Y)
        scrollbar_x.pack(side=tk.BOTTOM, fill=tk.X)
        
        # ربط النقر المزدوج
        self.buildings_tree.bind('<Double-1>', lambda e: self.view_building_details())
    
    def load_buildings(self):
        """تحميل المباني من قاعدة البيانات"""
        if not self.buildings_tree:
            return
            
        # مسح البيانات الحالية
        for item in self.buildings_tree.get_children():
            self.buildings_tree.delete(item)
        
        # إضافة بيانات تجريبية
        sample_buildings = [
            (1, "المبنى الإداري الرئيسي", "إداري", "الرياض - حي الملز", "5", "2500 م²", "نشط", "2020"),
            (2, "مبنى الهندسة المدنية", "أكاديمي", "الرياض - حي النخيل", "3", "1800 م²", "نشط", "2018"),
            (3, "مبنى الصيانة", "خدمي", "الرياض - المنطقة الصناعية", "2", "1200 م²", "نشط", "2019"),
            (4, "مبنى المختبرات", "تقني", "الرياض - حي الوادي", "4", "2200 م²", "قيد الصيانة", "2021"),
            (5, "مبنى المؤتمرات", "ثقافي", "الرياض - وسط المدينة", "2", "800 م²", "نشط", "2022")
        ]
        
        for building in sample_buildings:
            self.buildings_tree.insert('', 'end', values=building)
    
    def filter_buildings(self, event=None):
        """فلترة المباني حسب النص المدخل"""
        search_text = self.search_entry.get().lower()
        
        # مسح البيانات الحالية
        for item in self.buildings_tree.get_children():
            self.buildings_tree.delete(item)
        
        # إضافة البيانات المفلترة
        sample_buildings = [
            (1, "المبنى الإداري الرئيسي", "إداري", "الرياض - حي الملز", "5", "2500 م²", "نشط", "2020"),
            (2, "مبنى الهندسة المدنية", "أكاديمي", "الرياض - حي النخيل", "3", "1800 م²", "نشط", "2018"),
            (3, "مبنى الصيانة", "خدمي", "الرياض - المنطقة الصناعية", "2", "1200 م²", "نشط", "2019"),
            (4, "مبنى المختبرات", "تقني", "الرياض - حي الوادي", "4", "2200 م²", "قيد الصيانة", "2021"),
            (5, "مبنى المؤتمرات", "ثقافي", "الرياض - وسط المدينة", "2", "800 م²", "نشط", "2022")
        ]
        
        for building in sample_buildings:
            if not search_text or search_text in building[1].lower() or search_text in building[2].lower() or search_text in building[3].lower():
                self.buildings_tree.insert('', 'end', values=building)
    
    def add_building(self):
        """إضافة مبنى جديد"""
        dialog = BuildingDialog(self.window, self.db_manager, self.auth_manager, "إضافة مبنى جديد")
        if dialog.show():
            self.load_buildings()
    
    def edit_building(self):
        """تعديل مبنى محدد"""
        selected = self.buildings_tree.selection()
        if not selected:
            messagebox.showwarning("تحذير", "يرجى اختيار مبنى للتعديل")
            return
        
        building_data = self.buildings_tree.item(selected[0])['values']
        dialog = BuildingDialog(self.window, self.db_manager, self.auth_manager, "تعديل المبنى", building_data)
        if dialog.show():
            self.load_buildings()
    
    def delete_building(self):
        """حذف مبنى محدد"""
        selected = self.buildings_tree.selection()
        if not selected:
            messagebox.showwarning("تحذير", "يرجى اختيار مبنى للحذف")
            return
        
        building_name = self.buildings_tree.item(selected[0])['values'][1]
        
        if messagebox.askyesno("تأكيد الحذف", f"هل أنت متأكد من حذف المبنى '{building_name}'؟"):
            messagebox.showinfo("نجح", "تم حذف المبنى بنجاح")
            self.load_buildings()
    
    def view_building_details(self):
        """عرض تفاصيل المبنى"""
        selected = self.buildings_tree.selection()
        if not selected:
            messagebox.showwarning("تحذير", "يرجى اختيار مبنى لعرض التفاصيل")
            return
        
        building_data = self.buildings_tree.item(selected[0])['values']
        details = f"""
🏢 تفاصيل المبنى:

🆔 المعرف: {building_data[0]}
🏢 الاسم: {building_data[1]}
🏗️ النوع: {building_data[2]}
📍 الموقع: {building_data[3]}
🏢 عدد الطوابق: {building_data[4]}
📐 المساحة: {building_data[5]}
📊 الحالة: {building_data[6]}
📅 سنة البناء: {building_data[7]}
        """
        messagebox.showinfo("تفاصيل المبنى", details)

class BuildingDialog:
    """نافذة إضافة/تعديل مبنى"""
    
    def __init__(self, parent, db_manager, auth_manager, title, building_data=None):
        self.parent = parent
        self.db_manager = db_manager
        self.auth_manager = auth_manager
        self.title = title
        self.building_data = building_data
        self.result = False
        self.window = None
        self.setup_fonts()

    def setup_fonts(self):
        """إعداد الخطوط"""
        self.title_font = ("Segoe UI", 16, "bold")
        self.normal_font = ("Segoe UI", 12)
        self.button_font = ("Segoe UI", 11, "bold")
        self.label_font = ("Segoe UI", 11)

    def show(self):
        """عرض النافذة"""
        self.window = tk.Toplevel(self.parent)
        self.window.title(self.title)
        self.window.geometry("950x600")
        self.window.resizable(False, False)
        self.window.transient(self.parent)
        self.window.grab_set()
        
        self.create_form()
        
        # تفعيل زر الحفظ عند عرض النافذة
        if hasattr(self, 'save_button'):
            self.save_button.config(state="normal")
        
        # توسيط النافذة
        self.window.update_idletasks()
        x = (self.window.winfo_screenwidth() // 2) - (self.window.winfo_width() // 2)
        y = (self.window.winfo_screenheight() // 2) - (self.window.winfo_height() // 2)
        self.window.geometry(f"+{x}+{y}")
        
        self.window.wait_window()
        return self.result
    
    def create_form(self):
        """إنشاء نموذج المبنى"""
        # إطار رئيسي
        main_frame = ttk_bs.Frame(self.window, padding=25)
        main_frame.pack(fill=tk.BOTH, expand=True)

        # عنوان النموذج
        title_label = ttk_bs.Label(
            main_frame,
            text=f"🏢 {self.title} 🏢",
            bootstyle="primary",
            foreground="#1e3a8a"
        )
        title_label.grid(row=0, column=0, columnspan=4, pady=(0, 20), sticky="w")

        # الحقول في صفين وعمودين
        labels = [
            ("🏢 اسم المبنى: *", "name_entry", "info"),
            ("🏗️ نوع المبنى:", "type_combo", "info"),
            ("📍 الموقع:", "location_entry", "secondary"),
            ("🏢 عدد الطوابق:", "floors_entry", "warning"),
            ("📐 المساحة (م²):", "area_entry", "warning"),
            ("📊 الحالة:", "status_combo", "success"),
            ("📅 سنة البناء:", "year_entry", "info"),
            ("👷 المقاول:", "contractor_entry", "secondary"),
            ("💰 تكلفة البناء:", "cost_entry", "warning"),
            ("📝 ملاحظات:", "notes_text", None)
        ]

        self.entries = {}

        for idx, (label_text, attr_name, style) in enumerate(labels):
            row = (idx // 2) + 1
            col = (idx % 2) * 2
            ttk_bs.Label(main_frame, text=label_text).grid(row=row, column=col, sticky="w", pady=(0, 5), padx=(0, 10))
            
            if attr_name == "type_combo":
                combo = ttk_bs.Combobox(main_frame, width=45, bootstyle=style)
                combo['values'] = ("إداري", "أكاديمي", "خدمي", "تقني", "ثقافي", "سكني", "تجاري")
                combo.grid(row=row, column=col+1, pady=(0, 15), sticky="w")
                self.entries[attr_name] = combo
            elif attr_name == "status_combo":
                combo = ttk_bs.Combobox(main_frame, width=45, bootstyle=style)
                combo['values'] = ("نشط", "قيد الصيانة", "مغلق مؤقتاً", "قيد التطوير")
                combo.grid(row=row, column=col+1, pady=(0, 15), sticky="w")
                self.entries[attr_name] = combo
            elif attr_name == "notes_text":
                text = tk.Text(main_frame, height=3, width=45, wrap=tk.WORD)
                text.grid(row=row, column=col, columnspan=3, pady=(0, 20), sticky="w")
                self.entries[attr_name] = text
            else:
                entry = ttk_bs.Entry(main_frame, width=45, bootstyle=style)
                entry.grid(row=row, column=col+1, pady=(0, 15), sticky="w")
                self.entries[attr_name] = entry

        # ملء البيانات إذا كان تعديل
        if self.building_data:
            self.entries["name_entry"].insert(0, self.building_data[1])
            self.entries["type_combo"].set(self.building_data[2])
            self.entries["location_entry"].insert(0, self.building_data[3])
            self.entries["floors_entry"].insert(0, self.building_data[4])
            self.entries["area_entry"].insert(0, self.building_data[5].replace(" م²", ""))
            self.entries["status_combo"].set(self.building_data[6])
            self.entries["year_entry"].insert(0, self.building_data[7])
            if len(self.building_data) > 8:
                self.entries["contractor_entry"].insert(0, self.building_data[8] or "")
            if len(self.building_data) > 9:
                self.entries["cost_entry"].insert(0, str(self.building_data[9] or ""))
            if len(self.building_data) > 10:
                self.entries["notes_text"].insert('1.0', self.building_data[10] or "")

        # الأزرار
        button_frame = ttk_bs.Frame(main_frame)
        button_frame.grid(row=row+1, column=0, columnspan=4, pady=20, sticky="e")

        ttk_bs.Button(
            button_frame,
            text="💾 حفظ",
            command=self.save_building,
            bootstyle="success",
            width=18).pack(side=tk.RIGHT, padx=(15, 0), ipady=10)

        ttk_bs.Button(
            button_frame,
            text="❌ إلغاء",
            command=self.window.destroy,
            bootstyle="secondary",
            width=18).pack(side=tk.RIGHT, ipady=10)
    
    def save_building(self):
        """حفظ بيانات المبنى"""
        try:
            name = self.entries["name_entry"].get().strip()
            building_type = self.entries["type_combo"].get().strip()
            location = self.entries["location_entry"].get().strip()
            floors = self.entries["floors_entry"].get().strip()
            area = self.entries["area_entry"].get().strip()
            status = self.entries["status_combo"].get().strip()
            year = self.entries["year_entry"].get().strip()
            contractor = self.entries["contractor_entry"].get().strip()
            cost_str = self.entries["cost_entry"].get().strip()
            notes = self.entries["notes_text"].get('1.0', tk.END).strip()
        except KeyError as e:
            messagebox.showerror("خطأ", f"خطأ في الوصول إلى الحقل: {e}")
            return
        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في قراءة البيانات: {e}")
            return
        
        if not name:
            messagebox.showerror("خطأ", "يرجى إدخال اسم المبنى")
            return
        
        # تحويل التكلفة إلى رقم
        cost = None
        if cost_str:
            try:
                cost = float(cost_str)
            except ValueError:
                messagebox.showerror("خطأ", "يرجى إدخال تكلفة صحيحة")
                return
        
        # حفظ في قاعدة البيانات
        conn = self.db_manager.get_connection()
        cursor = conn.cursor()
        
        try:
            if self.building_data:  # تعديل
                cursor.execute('''
                    UPDATE buildings 
                    SET name=?, type=?, location=?, floors=?, area=?, status=?, 
                        construction_year=?, contractor=?, cost=?, notes=?, updated_at=CURRENT_TIMESTAMP
                    WHERE id=?
                ''', (name, building_type, location, floors, area, status, year, 
                      contractor, cost, notes, self.building_data[0]))
            else:  # إضافة جديد
                cursor.execute('''
                    INSERT INTO buildings (name, type, location, floors, area, status, 
                                         construction_year, contractor, cost, notes, created_by)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (name, building_type, location, floors, area, status, year,
                      contractor, cost, notes, self.auth_manager.current_user['id']))
            
            conn.commit()
            messagebox.showinfo("نجح", "تم حفظ بيانات المبنى بنجاح")
            self.result = True
            self.window.destroy()
            
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في حفظ البيانات: {str(e)}")
        finally:
            conn.close()

"""
وحدة إدارة الصيانة
Maintenance Management Module
"""

import tkinter as tk
from tkinter import ttk, messagebox
import ttkbootstrap as ttk_bs

class MaintenanceManagementWindow:
    """نافذة إدارة الصيانة"""
    
    def __init__(self, parent, db_manager, auth_manager):
        self.parent = parent
        self.db_manager = db_manager
        self.auth_manager = auth_manager
        self.window = None
        self.maintenance_tree = None
        self.setup_fonts()

    def setup_fonts(self):
        """إعداد الخطوط"""
        self.title_font = ("Segoe UI", 16, "bold")
        self.header_font = ("Segoe UI", 14, "bold")
        self.normal_font = ("Segoe UI", 12)
        self.button_font = ("Segoe UI", 11, "bold")
        self.tree_font = ("Segoe UI", 11)

    def show(self):
        """عرض نافذة إدارة الصيانة"""
        self.window = tk.Toplevel(self.parent)
        self.window.title("🔧 إدارة الصيانة")
        self.window.geometry("1100x650")
        self.window.resizable(True, True)

        # توسيط النافذة
        self.center_window()

        self.create_interface()
        self.load_maintenance_requests()
    
    def center_window(self):
        """توسيط النافذة في الشاشة"""
        self.window.update_idletasks()
        width = self.window.winfo_width()
        height = self.window.winfo_height()
        screen_width = self.window.winfo_screenwidth()
        screen_height = self.window.winfo_screenheight()
        x = (screen_width - width) // 2
        y = (screen_height - height) // 2
        self.window.geometry(f"{width}x{height}+{x}+{y}")
    
    def create_interface(self):
        """إنشاء واجهة إدارة الصيانة"""
        # عنوان الصفحة
        header_frame = ttk_bs.Frame(self.window)
        header_frame.pack(fill=tk.X, padx=10, pady=(10, 5))

        title_label = ttk_bs.Label(
            header_frame,
            text="🔧 إدارة الصيانة والأعطال 🔧",
            bootstyle="primary",
            foreground="#1e3a8a"
        )
        title_label.pack(side=tk.LEFT)

        # شريط الأدوات
        toolbar_frame = ttk_bs.LabelFrame(
            self.window,
            text="🛠️ أدوات الصيانة",
            padding=15,
            bootstyle="info"
        )
        toolbar_frame.pack(fill=tk.X, padx=15, pady=10)

        # الأزرار
        ttk_bs.Button(
            toolbar_frame,
            text="➕ بلاغ جديد",
            command=self.add_maintenance_request,
            bootstyle="success",
            width=18).pack(side=tk.LEFT, padx=8, ipady=8)

        ttk_bs.Button(
            toolbar_frame,
            text="✏️ تعديل",
            command=self.edit_maintenance_request,
            bootstyle="warning",
            width=15).pack(side=tk.LEFT, padx=8, ipady=8)

        ttk_bs.Button(
            toolbar_frame,
            text="✅ إنجاز",
            command=self.complete_maintenance,
            bootstyle="success",
            width=15).pack(side=tk.LEFT, padx=8, ipady=8)

        ttk_bs.Button(
            toolbar_frame,
            text="📋 تفاصيل",
            command=self.view_maintenance_details,
            bootstyle="info",
            width=15).pack(side=tk.LEFT, padx=8, ipady=8)

        ttk_bs.Button(
            toolbar_frame,
            text="🔄 تحديث",
            command=self.load_maintenance_requests,
            bootstyle="secondary",
            width=15).pack(side=tk.LEFT, padx=8, ipady=8)
        
        # شريط البحث والفلترة
        search_frame = ttk_bs.LabelFrame(
            self.window,
            text="🔍 البحث والفلترة",
            padding=15,
            bootstyle="secondary"
        )
        search_frame.pack(fill=tk.X, padx=15, pady=10)

        ttk_bs.Label(
            search_frame,
            text="🔍 البحث:"
        ).pack(side=tk.LEFT, padx=(0, 5))

        self.search_entry = ttk_bs.Entry(
            search_frame,
            width=30,
            bootstyle="info"
        )
        self.search_entry.pack(side=tk.LEFT, padx=5, ipady=3)
        self.search_entry.bind('<KeyRelease>', self.filter_maintenance)

        ttk_bs.Label(
            search_frame,
            text="📊 الحالة:"
        ).pack(side=tk.LEFT, padx=(20, 5))

        self.status_filter = ttk_bs.Combobox(
            search_frame,
            width=15,
            bootstyle="warning"
        )
        self.status_filter['values'] = ("الكل", "جديد", "قيد التنفيذ", "مكتمل", "مؤجل")
        self.status_filter.set("الكل")
        self.status_filter.pack(side=tk.LEFT, padx=5, ipady=3)
        self.status_filter.bind('<<ComboboxSelected>>', self.filter_maintenance)

        ttk_bs.Button(
            search_frame,
            text="🔍 بحث",
            command=self.filter_maintenance,
            bootstyle="info",
            width=10
        ).pack(side=tk.LEFT, padx=5, ipady=3)
        
        # جدول بلاغات الصيانة
        tree_frame = ttk_bs.LabelFrame(
            self.window,
            text="🔧 بلاغات الصيانة والأعطال",
            padding=15,
            bootstyle="primary"
        )
        tree_frame.pack(fill=tk.BOTH, expand=True, padx=15, pady=10)

        # إنشاء Treeview
        columns = ("id", "title", "building_id", "priority", "status", "reported_by", "assigned_to", "created_at", "notes")
        self.maintenance_tree = ttk.Treeview(
            tree_frame,
            columns=columns,
            show="headings",
            height=15
        )

        # تطبيق خط على الجدول
        style = ttk.Style()
        style.configure("Treeview", rowheight=30)
        style.configure("Treeview.Heading", foreground="#1e3a8a")
        
        # تعريف العناوين
        headers = {
            "id": "🆔 المعرف",
            "title": "📝 عنوان البلاغ",
            "building_id": "🏢 معرف المبنى",
            "priority": "⚡ الأولوية",
            "status": "📊 الحالة",
            "reported_by": "👤 المبلغ",
            "assigned_to": "👷 المكلف",
            "created_at": "📅 تاريخ الإنشاء",
            "notes": "📝 ملاحظات"
        }
        
        for col, header in headers.items():
            self.maintenance_tree.heading(col, text=header)
            if col == "id":
                self.maintenance_tree.column(col, width=50)
            elif col in ["title", "building_id"]:
                self.maintenance_tree.column(col, width=150)
            else:
                self.maintenance_tree.column(col, width=120)
        
        # شريط التمرير
        scrollbar_y = tk.Scrollbar(tree_frame, orient=tk.VERTICAL, command=self.maintenance_tree.yview)
        scrollbar_x = tk.Scrollbar(tree_frame, orient=tk.HORIZONTAL, command=self.maintenance_tree.xview)
        self.maintenance_tree.configure(yscrollcommand=scrollbar_y.set, xscrollcommand=scrollbar_x.set)
        
        # تعبئة الجدول وشريط التمرير
        self.maintenance_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        scrollbar_y.pack(side=tk.RIGHT, fill=tk.Y)
        scrollbar_x.pack(side=tk.BOTTOM, fill=tk.X)
        
        # ربط النقر المزدوج
        self.maintenance_tree.bind('<Double-1>', lambda e: self.view_maintenance_details())
    
    def load_maintenance_requests(self):
        """تحميل بلاغات الصيانة"""
        if not self.maintenance_tree:
            return
            
        # مسح البيانات الحالية
        for item in self.maintenance_tree.get_children():
            self.maintenance_tree.delete(item)
        
        # إضافة بيانات تجريبية
        sample_requests = [
            (1, "عطل في نظام التكييف", 101, "عالية", "قيد التنفيذ", "أحمد محمد", "فريق الصيانة", "2024/01/15", ""),
            (2, "تسريب في السباكة", 102, "متوسطة", "جديد", "سارة أحمد", "غير محدد", "2024/01/14", ""),
            (3, "عطل في الإضاءة", 103, "منخفضة", "مكتمل", "محمد علي", "علي حسن", "2024/01/13", ""),
            (4, "مشكلة في المصعد", 101, "عالية", "مؤجل", "فاطمة خالد", "شركة المصاعد", "2024/01/12", ""),
            (5, "تنظيف عام", 104, "متوسطة", "قيد التنفيذ", "عبدالله سعد", "فريق النظافة", "2024/01/11", ""),
            (6, "صيانة أجهزة الحاسوب", 102, "متوسطة", "جديد", "نورا عبدالله", "قسم التقنية", "2024/01/10", ""),
            (7, "إصلاح الأبواب", 105, "منخفضة", "مكتمل", "خالد محمد", "النجار محمد", "2024/01/09", "")
        ]
        
        for request in sample_requests:
            self.maintenance_tree.insert('', 'end', values=request)
    
    def filter_maintenance(self, event=None):
        """فلترة بلاغات الصيانة"""
        search_text = self.search_entry.get().lower()
        status_filter = self.status_filter.get()
        
        # مسح البيانات الحالية
        for item in self.maintenance_tree.get_children():
            self.maintenance_tree.delete(item)
        
        # البيانات التجريبية
        sample_requests = [
            (1, "عطل في نظام التكييف", 101, "عالية", "قيد التنفيذ", "أحمد محمد", "فريق الصيانة", "2024/01/15", ""),
            (2, "تسريب في السباكة", 102, "متوسطة", "جديد", "سارة أحمد", "غير محدد", "2024/01/14", ""),
            (3, "عطل في الإضاءة", 103, "منخفضة", "مكتمل", "محمد علي", "علي حسن", "2024/01/13", ""),
            (4, "مشكلة في المصعد", 101, "عالية", "مؤجل", "فاطمة خالد", "شركة المصاعد", "2024/01/12", ""),
            (5, "تنظيف عام", 104, "متوسطة", "قيد التنفيذ", "عبدالله سعد", "فريق النظافة", "2024/01/11", ""),
            (6, "صيانة أجهزة الحاسوب", 102, "متوسطة", "جديد", "نورا عبدالله", "قسم التقنية", "2024/01/10", ""),
            (7, "إصلاح الأبواب", 105, "منخفضة", "مكتمل", "خالد محمد", "النجار محمد", "2024/01/09", "")
        ]
        
        for request in sample_requests:
            # فلترة حسب النص
            text_match = (not search_text or 
                         search_text in request[1].lower() or 
                         search_text in str(request[2]).lower() or 
                         search_text in str(request[3]).lower())
            
            # فلترة حسب الحالة
            status_match = (status_filter == "الكل" or status_filter == request[4])
            
            if text_match and status_match:
                self.maintenance_tree.insert('', 'end', values=request)
    
    def add_maintenance_request(self):
        """إضافة بلاغ صيانة جديد"""
        dialog = MaintenanceDialog(self.window, self.db_manager, self.auth_manager, "إضافة بلاغ صيانة جديد")
        if dialog.show():
            self.load_maintenance_requests()
    
    def edit_maintenance_request(self):
        """تعديل بلاغ صيانة"""
        selected = self.maintenance_tree.selection()
        if not selected:
            messagebox.showwarning("تحذير", "يرجى اختيار بلاغ للتعديل")
            return
        
        request_data = self.maintenance_tree.item(selected[0])['values']
        dialog = MaintenanceDialog(self.window, self.db_manager, self.auth_manager, "تعديل بلاغ الصيانة", request_data)
        if dialog.show():
            self.load_maintenance_requests()
    
    def complete_maintenance(self):
        """إنجاز بلاغ الصيانة"""
        selected = self.maintenance_tree.selection()
        if not selected:
            messagebox.showwarning("تحذير", "يرجى اختيار بلاغ لإنجازه")
            return
        
        request_title = self.maintenance_tree.item(selected[0])['values'][1]
        
        if messagebox.askyesno("تأكيد الإنجاز", f"هل تم إنجاز '{request_title}' بنجاح؟"):
            messagebox.showinfo("نجح", "تم تحديث حالة البلاغ إلى 'مكتمل'")
            self.load_maintenance_requests()
    
    def view_maintenance_details(self):
        """عرض تفاصيل بلاغ الصيانة"""
        selected = self.maintenance_tree.selection()
        if not selected:
            messagebox.showwarning("تحذير", "يرجى اختيار بلاغ لعرض التفاصيل")
            return
        
        request_data = self.maintenance_tree.item(selected[0])['values']
        details = f"""
🔧 تفاصيل بلاغ الصيانة:

🆔 المعرف: {request_data[0]}
📝 العنوان: {request_data[1]}
🏢 المبنى: {request_data[2]}
🔧 نوع العطل: {request_data[3]}
⚡ الأولوية: {request_data[4]}
📊 الحالة: {request_data[5]}
👤 المبلغ: {request_data[6]}
📅 التاريخ: {request_data[7]}
👷 المكلف: {request_data[8]}
        """
        messagebox.showinfo("تفاصيل بلاغ الصيانة", details)

class MaintenanceDialog:
    """نافذة إضافة/تعديل بلاغ صيانة"""
    
    def __init__(self, parent, db_manager, auth_manager, title, request_data=None):
        self.parent = parent
        self.db_manager = db_manager
        self.auth_manager = auth_manager
        self.title = title
        self.request_data = request_data
        self.result = False
        self.window = None
        self.setup_fonts()

    def setup_fonts(self):
        """إعداد الخطوط"""
        self.title_font = ("Segoe UI", 16, "bold")
        self.normal_font = ("Segoe UI", 12)
        self.button_font = ("Segoe UI", 11, "bold")
        self.label_font = ("Segoe UI", 11)

    def show(self):
        """عرض النافذة"""
        self.window = tk.Toplevel(self.parent)
        self.window.title(self.title)
        self.window.geometry("950x600")
        self.window.resizable(False, False)
        self.window.transient(self.parent)
        self.window.grab_set()

        self.create_form()

        # تفعيل زر الحفظ عند عرض النافذة
        if hasattr(self, 'save_button'):
            self.save_button.config(state="normal")

        # توسيط النافذة
        self.window.update_idletasks()
        width = self.window.winfo_width()
        height = self.window.winfo_height()
        screen_width = self.window.winfo_screenwidth()
        screen_height = self.window.winfo_screenheight()
        x = (screen_width // 2) - (width // 2)
        y = (screen_height // 2) - (height // 2)
        self.window.geometry(f"{width}x{height}+{x}+{y}")

        self.window.wait_window()
        return self.result

    def create_form(self):
        """إنشاء نموذج بلاغ الصيانة"""
        # إطار رئيسي
        main_frame = ttk_bs.Frame(self.window, padding=25)
        main_frame.pack(fill=tk.BOTH, expand=True)

        # عنوان النموذج
        title_label = ttk_bs.Label(
            main_frame,
            text=f"🔧 {self.title} 🔧",
            bootstyle="primary",
            foreground="#1e3a8a"
        )
        title_label.grid(row=0, column=0, columnspan=4, pady=(0, 20), sticky="w")

        # الحقول في صفين وعمودين
        labels = [
            ("📝 عنوان البلاغ: *", "title_entry", "info"),
            ("📄 وصف المشكلة:", "description_text", None),
            ("🏢 المبنى:", "building_combo", "info"),
            ("🔧 نوع العطل:", "type_combo", "warning"),
            ("⚡ الأولوية:", "priority_combo", "danger"),
            ("📊 الحالة:", "status_combo", "success"),
            ("👷 المكلف بالصيانة:", "assigned_entry", "secondary"),
            ("📅 تاريخ البلاغ:", "date_entry", "info"),
            ("💰 التكلفة المقدرة:", "cost_entry", "warning"),
            ("📝 ملاحظات إضافية:", "notes_text", None)
        ]

        self.entries = {}

        for idx, (label_text, attr_name, style) in enumerate(labels):
            row = (idx // 2) + 1
            col = (idx % 2) * 2
            ttk_bs.Label(main_frame, text=label_text).grid(row=row, column=col, sticky="w", pady=(0, 5), padx=(0, 10))
            if attr_name in ["description_text", "notes_text"]:
                text = tk.Text(main_frame, height=4 if attr_name == "description_text" else 3, width=45, wrap=tk.WORD)
                text.grid(row=row, column=col+1, pady=(0, 15), sticky="w")
                self.entries[attr_name] = text
            elif attr_name.endswith("_combo"):
                combo = ttk_bs.Combobox(main_frame, width=45, bootstyle=style)
                if attr_name == "building_combo":
                    combo['values'] = ("المبنى الإداري", "مبنى الهندسة", "مبنى المختبرات", "مبنى الصيانة", "مبنى المؤتمرات")
                elif attr_name == "type_combo":
                    combo['values'] = ("تكييف", "سباكة", "كهرباء", "مصاعد", "تنظيف", "تقنية", "نجارة", "أخرى")
                elif attr_name == "priority_combo":
                    combo['values'] = ("عالية", "متوسطة", "منخفضة")
                elif attr_name == "status_combo":
                    combo['values'] = ("جديد", "قيد التنفيذ", "مكتمل", "مؤجل")
                combo.grid(row=row, column=col+1, pady=(0, 15), sticky="w")
                self.entries[attr_name] = combo
            else:
                entry = ttk_bs.Entry(main_frame, width=45, bootstyle=style)
                entry.grid(row=row, column=col+1, pady=(0, 15), sticky="w")
                self.entries[attr_name] = entry

        # ملء البيانات إذا كان تعديل
        if self.request_data:
            self.entries["title_entry"].insert(0, self.request_data[1])
            # وصف المشكلة - إضافة وصف افتراضي إذا لم يكن موجود
            description = f"وصف المشكلة: {self.request_data[1]}" if len(self.request_data) <= 2 else (self.request_data[2] or "")
            self.entries["description_text"].insert('1.0', description)
            # المبنى
            building_id = str(self.request_data[2]) if len(self.request_data) > 2 else "غير محدد"
            self.entries["building_combo"].set(f"مبنى رقم {building_id}")
            # نوع العطل - استخدام الأولوية كنوع مؤقت
            self.entries["type_combo"].set("تكييف" if self.request_data[3] == "عالية" else "عام")
            # الأولوية
            self.entries["priority_combo"].set(self.request_data[3])
            # الحالة
            self.entries["status_combo"].set(self.request_data[4])
            # المكلف
            self.entries["assigned_entry"].insert(0, self.request_data[6] if len(self.request_data) > 6 else "")
            # التاريخ
            self.entries["date_entry"].insert(0, self.request_data[7] if len(self.request_data) > 7 else "")
            # التكلفة - قيمة افتراضية
            self.entries["cost_entry"].insert(0, "0")
            # الملاحظات
            notes = self.request_data[8] if len(self.request_data) > 8 else ""
            self.entries["notes_text"].insert('1.0', notes)

        # الأزرار
        button_frame = ttk_bs.Frame(main_frame)
        button_frame.grid(row=row+1, column=0, columnspan=4, pady=20, sticky="e")

        self.save_button = ttk_bs.Button(
            button_frame,
            text="💾 حفظ",
            command=self.save_request,
            bootstyle="success",
            width=18)
        self.save_button.pack(side=tk.RIGHT, padx=(15, 0), ipady=10)

        self.cancel_button = ttk_bs.Button(
            button_frame,
            text="❌ إلغاء",
            command=self.window.destroy,
            bootstyle="secondary",
            width=18)
        self.cancel_button.pack(side=tk.RIGHT, ipady=10)
    
    def save_request(self):
        """حفظ بيانات بلاغ الصيانة"""
        try:
            title = self.entries["title_entry"].get().strip()
            description = self.entries["description_text"].get('1.0', tk.END).strip()
            building = self.entries["building_combo"].get().strip()
            request_type = self.entries["type_combo"].get().strip()
            priority = self.entries["priority_combo"].get().strip()
            status = self.entries["status_combo"].get().strip()
            assigned_to = self.entries["assigned_entry"].get().strip()
            date = self.entries["date_entry"].get().strip()
            cost_str = self.entries["cost_entry"].get().strip()
            notes = self.entries["notes_text"].get('1.0', tk.END).strip()
        except KeyError as e:
            messagebox.showerror("خطأ", f"خطأ في الوصول إلى الحقل: {e}")
            return
        except Exception as e:
            messagebox.showerror("خطأ", f"خطأ في قراءة البيانات: {e}")
            return
        
        if not title:
            messagebox.showerror("خطأ", "يرجى إدخال عنوان البلاغ")
            return
        
        # تحويل التكلفة إلى رقم
        cost = None
        if cost_str:
            try:
                cost = float(cost_str)
            except ValueError:
                messagebox.showerror("خطأ", "يرجى إدخال تكلفة صحيحة")
                return
        
        # حفظ في قاعدة البيانات
        conn = self.db_manager.get_connection()
        cursor = conn.cursor()
        
        try:
            if self.request_data:  # تعديل
                cursor.execute('''
                    UPDATE maintenance_requests 
                    SET title=?, description=?, building_id=?, priority=?, status=?, 
                        assigned_to=?, request_date=?, notes=?, updated_at=CURRENT_TIMESTAMP
                    WHERE id=?
                ''', (title, description, building, priority, status, 
                      assigned_to, date, notes, self.request_data[0]))
            else:  # إضافة جديد
                cursor.execute('''
                    INSERT INTO maintenance_requests (title, description, building_id, priority, 
                                                    status, assigned_to, request_date, notes, created_by)
                    VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?)
                ''', (title, description, building, priority, status,
                      assigned_to, date, notes, self.auth_manager.current_user['id']))
            
            conn.commit()
            messagebox.showinfo("نجح", "تم حفظ بلاغ الصيانة بنجاح")
            self.result = True
            self.window.destroy()
            
        except Exception as e:
            messagebox.showerror("خطأ", f"فشل في حفظ البيانات: {str(e)}")
        finally:
            conn.close()
